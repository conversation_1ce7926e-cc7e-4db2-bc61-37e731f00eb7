<!--企业产权档案-->
<template>
  <div class="custom-table-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="企业名称:">
          <el-input
            v-model="searchForm.zcepraEnterpriseName"
            placeholder="请输入企业名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="经济行为类型:">
          <el-select
            v-model="searchForm.zcepraTypesOfEconomicBehavior"
            placeholder="请选择经济行为类型"
            clearable
            style="width: 180px"
          >
            <el-option label="产权转让" value="产权转让" />
            <el-option label="增资扩股" value="增资扩股" />
            <el-option label="企业合并" value="企业合并" />
          </el-select>
        </el-form-item>
        <el-form-item label="历史时期:">
          <el-select
            v-model="searchForm.historicalPeriod"
            placeholder="请选择历史时期"
            clearable
            style="width: 150px"
          >
            <el-option label="时期1" value="时期1" />
            <el-option label="时期2" value="时期2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        :height="height"
        border
        stripe
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="index" label="序号" align="center" width="55"/>
        <el-table-column prop="zcepraEnterpriseName" label="企业名称" align="center" />
        <el-table-column prop="zcepraTypesOfEconomicBehavior" label="经济行为类型" align="center" width="125"/>
        <el-table-column prop="zcepraChangeInformation" label="变更信息" align="center" />
        <el-table-column prop="zcepraChangeDate" label="变更日期" align="center" width="95"/>
        <el-table-column label="操作" align="center" width="95">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDetail(scope.row)"
              style="color: #409EFF"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        background
        class="el-pagination-a"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchForm.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchForm.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>

    <!-- 详情弹窗 -->
    <BasicDialog
      title="企业产权档案详情"
      :visible.sync="detailDialogVisible"
      size="Middle"
      type="view"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <div class="dialog-content">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="基础与清算" name="basic">
            <div class="tab-content">
              <!-- 文件表格 -->
              <div class="file-table-section">
                <h3>注销法人资格</h3>
                <el-table
                  :data="fileTableData"
                  border
                  style="width: 100%"
                  class="file-table"
                >
                  <el-table-column prop="category" label="" width="200" align="left">
                    <template slot-scope="scope">
                      <span class="category-label">{{ scope.row.category }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="files" label="" align="left">
                    <template slot-scope="scope">
                      <div class="file-links-cell">
                        <div v-for="(file, index) in scope.row.files" :key="index" class="file-link-item">
                          <a href="#" class="file-link" @click.prevent="handleFileClick(file)">{{ file }}</a>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 国资委审核概况 -->
              <div class="review-section">
                <h3>国资委审核概况</h3>
                <div class="review-table">
                  <el-table
                    :data="reviewTableData"
                    border
                    style="width: 100%"
                    class="review-table-content"
                  >
                    <el-table-column prop="label" label="审核时间" width="200" align="center">
                      <template slot-scope="scope">
                        <span class="review-label">{{ scope.row.label }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="value" label="审核意见" align="center">
                      <template slot-scope="scope">
                        <span class="review-value">{{ scope.row.value }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="信息收集表" name="collection">
            <div class="tab-content">
              <div class="file-table-section">
                <h3>信息收集表</h3>
                <el-table
                  :data="collectionTableData"
                  border
                  style="width: 100%"
                  class="file-table"
                >
                  <el-table-column prop="category" label="收集项目" width="200" align="left">
                    <template slot-scope="scope">
                      <span class="category-label">{{ scope.row.category }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="content" label="内容详情" align="left">
                    <template slot-scope="scope">
                      <div class="file-links-cell">
                        <span class="review-value">{{ scope.row.content }}</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="合规性审查" name="compliance">
            <div class="tab-content">
              <div class="file-table-section">
                <h3>合规性审查</h3>
                <el-table
                  :data="complianceTableData"
                  border
                  style="width: 100%"
                  class="file-table"
                >
                  <el-table-column prop="category" label="审查项目" width="200" align="left">
                    <template slot-scope="scope">
                      <span class="category-label">{{ scope.row.category }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="审查状态" width="120" align="center">
                    <template slot-scope="scope">
                      <el-tag :type="scope.row.status === '通过' ? 'success' : 'warning'">
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="remark" label="备注" align="left">
                    <template slot-scope="scope">
                      <span class="review-value">{{ scope.row.remark }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="注销企业+企业" name="cancellation">
            <div class="tab-content">
              <div class="file-table-section">
                <h3>注销企业信息</h3>
                <el-table
                  :data="cancellationTableData"
                  border
                  style="width: 100%"
                  class="file-table"
                >
                  <el-table-column prop="category" label="注销项目" width="200" align="left">
                    <template slot-scope="scope">
                      <span class="category-label">{{ scope.row.category }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="files" label="相关文件" align="left">
                    <template slot-scope="scope">
                      <div class="file-links-cell">
                        <div v-for="(file, index) in scope.row.files" :key="index" class="file-link-item">
                          <a href="#" class="file-link" @click.prevent="handleFileClick(file)">{{ file }}</a>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </BasicDialog>
  </div>
</template>

<script>
import {
  enterprisePropertyGetList,
  enterprisePropertyGetDetail,
  enterprisePropertyUploadFile
} from '@/api/fileManagement/enterpriseProperty'
import BasicDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "EnterpriseProperty",
  components: {
    BasicDialog
  },
  data() {
    return {
      height: this.$baseTableHeight(1,1),
      // 搜索表单
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        zcepraEnterpriseName: '', // 企业名称
        zcepraTypesOfEconomicBehavior: '', // 经济行为类型
        zcepraChangeInformation: '', // 变更信息
        zcepraChangeDateStart: '', // 变更日期开始
        zcepraChangeDateEnd: '', // 变更日期结束
      },

      // 加载状态
      loading: false,

      // 表格数据
      tableData: [],

      // 分页总数
      total: 0,

      // 详情弹窗
      detailDialogVisible: false,
      activeTab: 'basic',

      // 文件表格数据
      fileTableData: [
        {
          category: '自营业申请文件',
          files: ['申请主体文件.pdf', '申请主体文件.doc']
        },
        {
          category: '集团申请文件',
          files: ['关于XXXXXX公司产权变更申请申请.pdf']
        },
        {
          category: '清算报告',
          files: ['XXXXXX公司清算报告.pdf']
        },
        {
          category: '注销公告',
          files: ['XXXXXX公司注销公告.pdf']
        },
        {
          category: 'XXX附件',
          files: ['XXXXXX公司XXXX附件.pdf']
        },
        {
          category: '其他附件',
          files: [
            'XXXXXX公司申请报告.pdf',
            'XXXXXX公司备案登记表.pdf',
            'XXXXXX公司备案登记报告.pdf',
            'XXXXXX公司备案会议纪要.pdf'
          ]
        }
      ],

      // 审核表格数据
      reviewTableData: [
        {
          label: '2060 / 08 / 01 02:34',
          value: '资料齐全，同意办理'
        }
      ],

      // 信息收集表数据
      collectionTableData: [
        {
          category: '企业基本信息',
          content: '企业名称、注册地址、法定代表人等基本信息已收集完整'
        },
        {
          category: '财务状况',
          content: '资产负债表、利润表、现金流量表等财务报表已收集'
        },
        {
          category: '经营状况',
          content: '主营业务、经营范围、市场地位等信息已收集'
        },
        {
          category: '法律文件',
          content: '营业执照、章程、重要合同等法律文件已收集'
        }
      ],

      // 合规性审查数据
      complianceTableData: [
        {
          category: '法律合规性',
          status: '通过',
          remark: '企业经营活动符合相关法律法规要求'
        },
        {
          category: '财务合规性',
          status: '通过',
          remark: '财务报表真实准确，会计处理符合准则'
        },
        {
          category: '税务合规性',
          status: '待审核',
          remark: '税务申报情况需进一步核实'
        },
        {
          category: '环保合规性',
          status: '通过',
          remark: '环保手续齐全，无违法违规行为'
        }
      ],

      // 注销企业数据
      cancellationTableData: [
        {
          category: '注销申请书',
          files: ['企业注销申请书.pdf', '股东会决议.pdf']
        },
        {
          category: '清算报告',
          files: ['清算报告.pdf', '清算组备案证明.pdf']
        },
        {
          category: '税务清算',
          files: ['税务清算证明.pdf', '完税证明.pdf']
        },
        {
          category: '社保清算',
          files: ['社保清算证明.pdf']
        },
        {
          category: '银行账户',
          files: ['银行账户注销证明.pdf']
        }
      ]
    }
  },

  methods: {
    // 搜索
    handleSearch() {
      this.searchForm.pageNo = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.searchForm.pageSize = val
      this.searchForm.pageNo = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.searchForm.pageNo = val
      this.loadData()
    },

    // 查看详情
    async handleDetail(row) {
      try {
        // 获取详情数据
        const detailRes = await enterprisePropertyGetDetail(row.zcepraId)
        if (detailRes.code === '200') {
          console.log('详情数据:', detailRes.data)
        }
        // 使用 mock 文件数据
        this.loadMockFileData()
        this.detailDialogVisible = true
        this.activeTab = 'basic'
      } catch (error) {
        console.error('获取详情失败:', error)
        // 使用 mock 文件数据
        this.loadMockFileData()
        this.detailDialogVisible = true
        this.activeTab = 'basic'
      }
    },

    // 关闭弹窗
    handleDialogClose() {
      this.detailDialogVisible = false
      this.activeTab = 'basic'
    },

    // 加载 Mock 文件数据
    loadMockFileData() {
      const mockFiles = [
        { category: '自营业申请文件', fileName: '申请主体文件.pdf' },
        { category: '自营业申请文件', fileName: '申请主体文件.doc' },
        { category: '集团申请文件', fileName: '关于XXXXXX公司产权变更申请申请.pdf' },
        { category: '清算报告', fileName: 'XXXXXX公司清算报告.pdf' },
        { category: '注销公告', fileName: 'XXXXXX公司注销公告.pdf' },
        { category: 'XXX附件', fileName: 'XXXXXX公司XXXX附件.pdf' },
        { category: '其他附件', fileName: 'XXXXXX公司申请报告.pdf' },
        { category: '其他附件', fileName: 'XXXXXX公司备案登记表.pdf' },
        { category: '其他附件', fileName: 'XXXXXX公司备案登记报告.pdf' },
        { category: '其他附件', fileName: 'XXXXXX公司备案会议纪要.pdf' }
      ]
      this.updateFileTableData(mockFiles)
    },

    // 更新文件表格数据
    updateFileTableData(files) {
      // 根据文件类型分组
      const fileGroups = {
        '自营业申请文件': [],
        '集团申请文件': [],
        '清算报告': [],
        '注销公告': [],
        'XXX附件': [],
        '其他附件': []
      }

      files.forEach(file => {
        const category = file.category || '其他附件'
        if (fileGroups[category]) {
          fileGroups[category].push(file.fileName)
        } else {
          fileGroups['其他附件'].push(file.fileName)
        }
      })

      this.fileTableData = Object.keys(fileGroups).map(category => ({
        category,
        files: fileGroups[category]
      })).filter(item => item.files.length > 0)
    },

    // 文件点击处理
    handleFileClick(fileName) {
      this.$message.info(`点击了文件: ${fileName}`)
      // 这里可以添加文件下载或预览逻辑
      // 由于 beijing-API 中没有专门的文件下载接口，这里暂时只显示提示
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const response = await enterprisePropertyGetList(this.searchForm)
        console.log(response);
        if (response.code == 200) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.loading = false
      } finally {
        this.loading = false
      }
    },
  },

  mounted() {
    this.loadData()
  }
}
</script>

<style scoped>
.enterprise-property {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

.title i {
  margin-right: 8px;
  color: #e74c3c;
  font-size: 20px;
}

.search-section {
  background: white;
  padding: 0 20px 20px 20px;
  border-radius: 4px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-weight: normal;
  color: #666;
}

.table-section {
  background: white;
  border-radius: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}



.dialog-content {
  padding: 20px;
}

.tab-content {
  padding: 0;
  min-height: 400px;
}

/* 文件表格样式 */
.file-table-section {
  margin-bottom: 30px;
  text-align: center;
}

.file-table-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  text-align: center;
  padding: 10px 0;
  border-bottom: 2px solid #e4e7ed;
}

.file-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.file-table .el-table__header th {
  background: #f5f7fa;
  border: none;
  height: 0;
  padding: 0;
}

.file-table .el-table__header th .cell {
  display: none;
}

.file-table .el-table__body td {
  border-right: 1px solid #e4e7ed;
  padding: 15px 20px;
  vertical-align: top;
  background-color: #fff;
}

.file-table .el-table__body tr:hover td {
  background-color: #f5f7fa;
}

.category-label {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
  display: block;
  text-align: left;
  line-height: 24px;
}

.file-links-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-link-item {
  margin-bottom: 5px;
}

.file-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 13px;
  cursor: pointer;
  transition: color 0.3s;
}

.file-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.review-section {
  border-top: 2px solid #eee;
  padding-top: 20px;
}

.review-section h3 {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.review-table {
  margin-top: 15px;
}

.review-table-content {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.review-table-content .el-table__header th {
  background: #f5f7fa;
  color: #333;
  font-weight: bold;
  text-align: center;
  border-right: 1px solid #e4e7ed;
  padding: 12px 8px;
}

.review-table-content .el-table__body td {
  border-right: 1px solid #e4e7ed;
  padding: 15px 20px;
  text-align: center;
  background-color: #fff;
}

.review-table-content .el-table__body tr:hover td {
  background-color: #f5f7fa;
}

.review-label {
  color: #333;
  font-size: 13px;
}

.review-value {
  color: #333;
  font-size: 13px;
}



/* 表格样式优化 */
.el-table {
  font-size: 13px;
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background: #f5f7fa;
  color: #333;
  font-weight: bold;
  border-bottom: 1px solid #e4e7ed;
}

.el-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.el-table .el-table__body tr:hover td {
  background-color: #f5f7fa;
}

/* 弹窗内表格特殊样式 */
.dialog-content .el-table {
  margin-bottom: 20px;
}

.dialog-content .el-table th .cell {
  font-weight: 600;
  color: #606266;
}

.dialog-content .el-table td .cell {
  color: #303133;
}

/* 标签页样式 */
.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
  border-right: 1px solid #e4e7ed;
  color: #606266;
  font-weight: 500;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background-color: #fff;
  color: #409EFF;
  border-bottom-color: #fff;
}

.el-tabs--card > .el-tabs__content {
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-top: none;
  border-radius: 0 0 4px 4px;
  min-height: 500px;
}

/* 标签页内容统一样式 */
.el-tab-pane {
  padding: 0;
}

.el-tab-pane .tab-content {
  padding: 0;
}

/* 状态标签样式 */
.el-tag {
  font-weight: 500;
}

.el-tag--success {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
  border-right: 1px solid #e4e7ed;
  color: #666;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background: #409EFF;
  color: white;
  border-color: #409EFF;
}
</style>
